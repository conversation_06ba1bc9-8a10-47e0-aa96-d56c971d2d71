<template>
  <div v-if="visible" class="floating-info-card">
    <!-- 关闭按钮 (仅天气卡片使用) -->
    <div v-if="cardType === 'weather'" class="close-btn" @click="handleClose">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M18 6L6 18M6 6L18 18"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>

    <!-- 天气信息卡片 -->
    <div v-if="cardType === 'weather'" class="card-content weather-card">
      <div class="card-header">
        <div class="card-icon weather-icon" @click="handlePlayWeatherContent">
          <img src="@/assets/img/langdu_kaiqi.png" alt="朗读" />
        </div>
        <div class="card-title-section">
          <h3 class="card-title">天气解读和预警</h3>
          <p class="card-subtitle">📍 AI智能分析</p>
        </div>
      </div>

      <div class="weather-content">
        <!-- 加载状态 -->
        <div v-if="loadingWeather" class="loading-state">
          <div class="loading-spinner"></div>
          <span class="loading-text">正在获取天气信息...</span>
        </div>

        <!-- AI提醒内容 -->
        <div v-else-if="weatherApiData && weatherApiData.ai_reminder" class="ai-reminder-content">
          <div
            class="ai-reminder-text markdown-body custom-markdown-body"
            v-html="renderMarkdown(weatherApiData.ai_reminder)"
          ></div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="weatherError" class="error-state">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ weatherError }}</div>
        </div>

        <!-- 默认状态 -->
        <div v-else class="default-state">
          <div class="default-text">暂无天气信息</div>
        </div>
      </div>
    </div>

    <!-- 记录您的情况卡片 -->
    <div v-else-if="cardType === 'record'" class="card-content record-card">
      <div class="record-row">
        <div class="card-icon record-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14 2V8H20"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <p class="record-text">跟老董聊天让我更懂你吧</p>
        <div class="record-close-btn" @click="handleClose">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M18 6L6 18M6 6L18 18"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>

      <!-- 朝下的箭头 -->
      <div class="record-arrow" @click="handleToggleUserInfo">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          :class="{ rotated: showUserInfo }"
        >
          <path
            d="M6 9L12 15L18 9"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>

      <!-- PersonDetail内容区域 -->
      <div v-if="showUserInfo" class="person-detail-content">
        <!-- 加载状态 -->
        <div v-if="loadingUserProfile" class="loading-state">
          <div class="loading-spinner"></div>
          <span class="loading-text">正在获取用户信息...</span>
        </div>

        <!-- PersonDetail各个Section -->
        <div v-else-if="userProfileData" class="content-sections">
          <!-- 记忆时刻组件 -->
          <MemorySection
            v-if="props.userId"
            ref="memorySectionRef"
            :person-detail="userProfileData"
            :person-id="props.userId"
            :user-id="props.userId"
            @memory-add="handleMemoryAdd"
            @edit-memory="handleEditMemory"
            @delete-memory="handleDeleteMemory"
          />

          <!-- 个人信息组件 -->
          <InfoSection
            v-if="props.userId"
            :person-detail="userProfileData"
            :person-id="props.userId"
            :user-id="props.userId"
            @basic-info-mic-click="handleBasicInfoMicClick"
            @person-updated="handlePersonUpdated"
          />

          <!-- 天气分析组件 -->
          <WeatherSection
            v-if="props.userId"
            :person-detail="userProfileData"
            :person-id="props.userId"
            :user-id="props.userId"
            @weather-mic-click="handleWeatherMicClick"
            @person-updated="handlePersonUpdated"
          />

          <!-- 推荐话题组件 -->
          <TopicSection
            v-if="props.userId"
            :person-id="props.userId"
            :user-id="props.userId"
            @topic-click="handleTopicClick"
          />

          <!-- 生活方式组件（去过&想去、饮食偏好、期望、其他属性） -->
          <LifestyleSection
            v-if="props.userId"
            :person-detail="userProfileData"
            :person-id="props.userId"
            :user-id="props.userId"
            @attributes-updated="handleAttributesUpdated"
            @show-voice-chat="handleShowVoiceChat"
          />
        </div>

        <!-- 错误状态 -->
        <div v-else-if="userProfileError" class="error-state">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ userProfileError }}</div>
        </div>

        <!-- 默认状态 -->
        <div v-else class="default-state">
          <div class="default-text">暂无用户信息</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, onMounted, type Ref } from 'vue';
import {
  getComprehensiveWeather,
  type IComprehensiveWeatherResponse,
  type IPersonDetail,
  type IEvent,
  type IRecommendedTopic,
} from '@/apis/memory';
import { getUserProfile, type IPersonData } from '@/apis/relation';
import { weatherRefreshManager, type IWeatherRefreshEvent } from '@/utils/weatherRefresh';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';
import MarkdownIt from 'markdown-it';
import 'github-markdown-css/github-markdown-light.css';
import '@/styles/markdown.scss';

// 导入PersonDetail中使用的各个Section组件
import MemorySection from '@/components/Sections/MemorySection.vue';
import InfoSection from '@/components/Sections/InfoSection.vue';
import WeatherSection from '@/components/Sections/WeatherSection.vue';
import TopicSection from '@/components/Sections/TopicSection.vue';
import LifestyleSection from '@/components/Sections/LifestyleSection.vue';

// 定义props
interface IProps {
  visible: boolean;
  cardType: 'weather' | 'record' | null;
  userId?: string;
}

const props = defineProps<IProps>();

// 定义emits
const emit = defineEmits<{
  close: [];
}>();

// 创建简化的markdown实例
const md = new MarkdownIt({
  html: false, // 不允许HTML标签
  breaks: true, // 支持换行
  linkify: true, // 自动识别链接
});

// markdown渲染函数
const renderMarkdown = (text: string): string => {
  return md.render(text);
};

// 音频播放相关
const { play, stop, isCurrentAudioPlaying } = useAudioQueue();

// 天气API相关状态
const loadingWeather = ref(false);
const weatherApiData: Ref<IComprehensiveWeatherResponse | null> = ref(null);
const weatherError = ref<string>('');

// 用户信息相关状态
const showUserInfo = ref(true); // 默认展开状态
const loadingUserProfile = ref(false);
const userProfileData: Ref<IPersonData | null> = ref(null);
const userProfileError = ref<string>('');

// PersonDetail相关ref
const memorySectionRef = ref<InstanceType<typeof MemorySection> | null>(null);

// 定时器相关
let weatherUpdateTimer: NodeJS.Timeout | null = null;
let userProfileUpdateTimer: NodeJS.Timeout | null = null;

// 天气数据刷新订阅
let weatherRefreshUnsubscribe: (() => void) | null = null;

// 处理天气数据刷新事件
const handleWeatherRefresh = (event: IWeatherRefreshEvent) => {
  if (event.type === 'comprehensive-weather' && event.userId === props.userId) {
    console.log('🔄 [FloatingInfoCard] 收到综合天气数据刷新事件，更新ai-reminder-content');
    // 更新天气数据
    weatherApiData.value = event.data as IComprehensiveWeatherResponse;
    weatherError.value = '';
  }
};

// 获取天气数据
const loadWeatherData = async () => {
  if (!props.userId) {
    weatherError.value = '缺少用户信息，无法获取天气数据';
    return;
  }

  try {
    loadingWeather.value = true;
    weatherError.value = '';

    console.log('🔄 [FloatingInfoCard.vue] 开始获取综合天气数据...', {
      userId: props.userId,
    });

    const response = await getComprehensiveWeather({
      user_id: props.userId,
    });

    console.log('📡 [FloatingInfoCard.vue] 综合天气数据响应:', response);

    if (response && response.result === 'success') {
      weatherApiData.value = response;
      console.log('✅ [FloatingInfoCard.vue] 综合天气数据加载成功');
    } else {
      weatherError.value = '天气数据获取失败';
      console.warn('⚠️ [FloatingInfoCard.vue] 综合天气数据格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard.vue] 获取综合天气数据失败:', error);
    weatherError.value = '网络请求失败，请稍后重试';
  } finally {
    loadingWeather.value = false;
  }
};

// 获取用户档案数据
const loadUserProfileData = async () => {
  if (!props.userId) {
    userProfileError.value = '缺少用户信息，无法获取用户档案';
    return;
  }

  try {
    loadingUserProfile.value = true;
    userProfileError.value = '';

    console.log('🔄 [FloatingInfoCard.vue] 开始获取用户档案数据...', {
      userId: props.userId,
    });

    const response = await getUserProfile({
      user_id: props.userId,
    });

    console.log('📡 [FloatingInfoCard.vue] 用户档案数据响应:', response);

    if (response && response.result === 'success' && response.person) {
      userProfileData.value = response.person;
      console.log('✅ [FloatingInfoCard.vue] 用户档案数据加载成功');
    } else {
      userProfileError.value = response?.reason || '用户档案数据获取失败';
      console.warn('⚠️ [FloatingInfoCard.vue] 用户档案数据格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard.vue] 获取用户档案数据失败:', error);
    userProfileError.value = '网络请求失败，请稍后重试';
  } finally {
    loadingUserProfile.value = false;
  }
};

// 启动天气数据定时更新
const startWeatherTimer = () => {
  // 清除现有定时器
  clearWeatherTimer();

  // 立即加载一次数据
  void loadWeatherData();

  // 注释掉定时器，关闭定时更新功能
  // weatherUpdateTimer = setInterval(() => {
  //   void loadWeatherData();
  // }, 60000); // 60秒 = 60000毫秒

  console.log('🔄 [FloatingInfoCard.vue] 天气数据已加载，定时更新已关闭');
};

// 启动用户档案数据定时更新
const startUserProfileTimer = () => {
  // 清除现有定时器
  clearUserProfileTimer();

  // 立即加载一次数据
  void loadUserProfileData();

  // 启动定时器，每30秒更新一次
  userProfileUpdateTimer = setInterval(() => {
    void loadUserProfileData();
  }, 30000); // 30秒 = 30000毫秒

  console.log('🔄 [FloatingInfoCard.vue] 用户档案数据定时更新已启动');
};

// 清除天气数据定时器
const clearWeatherTimer = () => {
  if (weatherUpdateTimer) {
    clearInterval(weatherUpdateTimer);
    weatherUpdateTimer = null;
    console.log('⏹️ [FloatingInfoCard.vue] 天气数据定时器已清除');
  }
};

// 清除用户档案数据定时器
const clearUserProfileTimer = () => {
  if (userProfileUpdateTimer) {
    clearInterval(userProfileUpdateTimer);
    userProfileUpdateTimer = null;
    console.log('⏹️ [FloatingInfoCard.vue] 用户档案数据定时器已清除');
  }
};

// 监听visible和cardType变化，当天气卡片显示时启动定时器
watch(
  () => [props.visible, props.cardType, props.userId],
  ([visible, cardType, userId]) => {
    if (visible && cardType === 'weather' && userId) {
      startWeatherTimer();
    } else {
      clearWeatherTimer();
    }

    // 当record卡片显示时，自动启动用户信息加载
    if (visible && cardType === 'record' && userId) {
      startUserProfileTimer();
    } else if (cardType !== 'record') {
      clearUserProfileTimer();
    }
  },
  { immediate: true }, // 在 watch 选项中，immediate: true 会让观察者在初始化时立即执行一次，无论被观察的属性是否发生变化。
);

// 监听用户信息显示状态，当显示时启动定时器
watch(
  () => [showUserInfo.value, props.userId],
  ([show, userId]) => {
    if (show && userId && props.cardType === 'record') {
      startUserProfileTimer();
    } else {
      clearUserProfileTimer();
    }
  },
  { immediate: false },
);

// 组件挂载时订阅天气数据刷新事件
onMounted(() => {
  weatherRefreshUnsubscribe = weatherRefreshManager.subscribe(handleWeatherRefresh);
});

// 处理关闭
const handleClose = () => {
  emit('close');
};

// 处理切换用户信息显示
const handleToggleUserInfo = () => {
  showUserInfo.value = !showUserInfo.value;
};



// 处理朗读天气内容
const handlePlayWeatherContent = () => {
  // 获取要朗读的文本内容
  let textToRead = '';

  if (loadingWeather.value) {
    textToRead = '正在获取天气信息...';
  } else if (weatherApiData.value && weatherApiData.value.ai_reminder) {
    textToRead = weatherApiData.value.ai_reminder;
  } else if (weatherError.value) {
    textToRead = weatherError.value;
  } else {
    textToRead = '暂无天气信息';
  }

  // 生成唯一ID用于音频播放
  const audioId = `weather-content-${Date.now()}`;

  if (isCurrentAudioPlaying(audioId)) {
    stop();
  } else {
    play({
      id: audioId,
      text: textToRead,
      type: 'manualPlay',
    });
  }
};

// PersonDetail相关事件处理函数
const handleMemoryAdd = () => {
  console.log('🔄 [FloatingInfoCard] 记忆添加事件');
  // 可以在这里添加具体的处理逻辑
};

const handleEditMemory = (memory: IEvent) => {
  console.log('🔄 [FloatingInfoCard] 记忆编辑事件:', memory);
  // 可以在这里添加具体的处理逻辑
};

const handleDeleteMemory = (memory: IEvent) => {
  console.log('🔄 [FloatingInfoCard] 记忆删除事件:', memory);
  // 可以在这里添加具体的处理逻辑
};

const handleBasicInfoMicClick = () => {
  console.log('🔄 [FloatingInfoCard] 个人信息语音点击');
  // 可以在这里添加具体的处理逻辑
};

const handlePersonUpdated = (personDetail: IPersonDetail) => {
  console.log('🔄 [FloatingInfoCard] 人员信息更新:', personDetail);
  // 更新本地数据
  userProfileData.value = personDetail as IPersonData;
};

const handleWeatherMicClick = () => {
  console.log('🔄 [FloatingInfoCard] 天气分析语音点击');
  // 可以在这里添加具体的处理逻辑
};

const handleTopicClick = (topic: IRecommendedTopic) => {
  console.log('🔄 [FloatingInfoCard] 话题点击:', topic);
  // 可以在这里添加具体的处理逻辑
};

const handleAttributesUpdated = (newAttributes: Record<string, string>) => {
  console.log('🔄 [FloatingInfoCard] 属性更新:', newAttributes);
  // 可以在这里添加具体的处理逻辑
};

const handleShowVoiceChat = (sectionInfo: { title: string; icon: string; content: string }) => {
  console.log('🔄 [FloatingInfoCard] 显示语音聊天:', sectionInfo);
  // 可以在这里添加具体的处理逻辑
};

// 组件卸载时清除定时器
onUnmounted(() => {
  clearWeatherTimer();
  clearUserProfileTimer();

  // 取消天气数据刷新订阅
  if (weatherRefreshUnsubscribe) {
    weatherRefreshUnsubscribe();
    weatherRefreshUnsubscribe = null;
  }
});
</script>

<style lang="scss" scoped>
.floating-info-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin: 16px 32px;
  position: relative;
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  border: 2px solid var(--border-accent);
  animation: slideInDown 0.3s ease-out;
  z-index: 10;
  max-width: 640px;
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  border: 1px solid var(--border-glass);

  &:hover {
    background: var(--primary-color-medium);
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-accent);
  }
}

.card-content {
  width: 100%;
}

.card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 16px;
}

.card-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 2px solid var(--border-accent);
  color: var(--accent-color);
  flex-shrink: 0;
  transition: all 0.3s ease;
  cursor: pointer;

  img {
    width: 32px;
    height: 32px;
    object-fit: contain;
  }

  &:hover {
    background: var(--primary-color-medium);
    transform: translateY(-2px);
    box-shadow: var(--shadow-accent);
  }
}

.card-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.card-subtitle {
  font-size: var(--font-size-xl);
  font-weight: 400;
  color: var(--text-tertiary);
  margin: 0;
  line-height: 1.3;
}

.weather-content {
  width: 100%;
}

// 加载状态样式
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 28px;
  height: 28px;
  border: 2px solid var(--border-glass);
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 22px;
}

// AI提醒内容样式
.ai-reminder-content {
  max-height: 250px;
  padding: 20px;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-glass);
  border-left: 3px solid var(--accent-color);
  overflow-y: auto;
}

.ai-reminder-text {
  font-size: calc(var(--font-size-lg) + 4px);
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
}

// 错误状态样式
.error-state {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.error-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

// 默认状态样式
.default-state {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
}

.default-text {
  font-size: var(--font-size-base);
}

.weather-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 12px;
}

.weather-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-glass);
  border-left: 3px solid var(--accent-color);
  transition: all 0.3s ease;
  gap: 12px;

  &:hover {
    background: var(--primary-color-medium);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }
}

.weather-item-icon {
  font-size: var(--font-size-2xl);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.weather-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label {
  font-size: var(--font-size-2xl);
  color: var(--text-tertiary);
  font-weight: 500;
  line-height: 1.2;
}

.value {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.2;
}

.record-row {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.record-text {
  flex: 1;
  font-size: var(--font-size-3xl);
  color: var(--text-primary);
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
  text-align: center;
}

.record-close-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  border: 1px solid var(--border-glass);
  flex-shrink: 0;

  &:hover {
    background: var(--primary-color-medium);
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-accent);
  }
}

.record-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  cursor: pointer;
  color: var(--text-tertiary);
  transition: all 0.3s ease;

  svg {
    transition: transform 0.3s ease;

    &.rotated {
      transform: rotate(180deg);
    }
  }

  &:hover {
    color: var(--accent-color);
    transform: translateY(-2px);
  }
}

// PersonDetail内容区域样式
.person-detail-content {
  margin-top: 20px;
  animation: slideInDown 0.3s ease-out;
}

.content-sections {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 600px; // 限制最大高度，允许滚动
}

// PersonDetail各个Section的样式 - 与PersonDetailPopup保持一致
.content-sections :deep(.memory-section),
.content-sections :deep(.info-section),
.content-sections :deep(.weather-section),
.content-sections :deep(.topic-section),
.content-sections :deep(.travel-section),
.content-sections :deep(.food-section),
.content-sections :deep(.expectation-section),
.content-sections :deep(.other-attributes-section) {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(1, 28, 32, 0.4);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// Section标题样式
.content-sections :deep(.section-header) {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }
}

// Section内容样式
.content-sections :deep(.section-content) {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 信息显示区域样式
.content-sections :deep(.info-display-section) {
  .info-display-content {
    .info-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 24px;

      .info-label {
        color: #00ffff;
        font-weight: 500;
        min-width: 80px;
        margin-right: 8px;
        font-size: 30px;
      }

      .info-value {
        color: white;
        flex: 1;
        word-break: break-word;
        font-size: 30px;
      }
    }

    .attributes-display {
      margin-top: 12px;
    }
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
