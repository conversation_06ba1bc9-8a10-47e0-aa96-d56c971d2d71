<template>
  <div class="memo-sidebar-container">
    <!-- 遮罩层 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="handleClose"></div>
    <!-- 侧边栏 -->
    <div class="memo-sidebar" :class="{ 'sidebar-open': isOpen }">
      <div class="sidebar-header">
        <div class="title">备忘录</div>
        <div class="intimacy-display">
          <span class="intimacy-label">当前懂量：</span>
          <span class="intimacy-value">{{ intimacyScore }}</span>
        </div>
        <div class="close-btn" @click="handleClose">
          <img src="@/assets/img/close.png" alt="关闭" />
        </div>
      </div>
      <div class="sidebar-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-text">{{ error }}</div>
          <div class="retry-btn" @click="fetchMemoData">重试</div>
        </div>
        <!-- 备忘录内容 -->
        <div v-else class="memo-list">
          <!-- 动态渲染分类 -->
          <div v-for="category in memoCategories" :key="category.title" class="memo-category">
            <div class="category-title">{{ category.title }}</div>
            <div class="memo-items">
              <div v-for="item in category.items" :key="item.label" class="memo-item">
                <div class="memo-content">
                  <div class="memo-label">{{ item.label }}</div>
                  <div class="memo-value">{{ item.value }}</div>
                </div>
                <div class="memo-actions">
                  <div class="delete-btn" @click.stop="handleDeleteMemoItem(category.title, item)">
                    <img src="@/assets/icon/delete.png" alt="删除" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 空状态 -->
          <div v-if="memoCategories.length === 0" class="empty-state">
            <div class="empty-text">暂无备忘录信息</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    :content="deleteDialogContent"
    :hint="deleteDialogHint"
    :is-loading="isDeleting"
    @confirm="confirmDeleteMemoItem"
    @cancel="closeDeleteDialog"
  />
</template>

<script setup lang="ts">
import { watch, ref, onMounted } from 'vue';
import { showSuccessToast, showFailToast } from 'vant';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import fetchInstance from '@/lib/fetch';
import { useUserStore } from '@/stores/user';
import { getMemoInfo, type IMemoCategory, type IMemoItem, type IEventItem } from '../../apis/memo';
import { getUserInfo } from '../../apis/common';
import { getIntimacy, deletePersonEvent } from '../../apis/memory';
import { updatePerson } from '../../apis/relation';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close']);

// 响应式数据
const loading = ref(false);
const error = ref('');
const memoCategories = ref<IMemoCategory[]>([]);
const intimacyScore = ref<number | string>('--');

// 删除相关状态
const showDeleteDialog = ref(false);
const isDeleting = ref(false);
const deleteDialogContent = ref('');
const deleteDialogHint = ref('');
const itemToDelete = ref<{
  category: string;
  item: IMemoItem;
  type: 'event' | 'attribute';
  eventId?: string;
} | null>(null);

// 用户存储
const userStore = useUserStore();

// 存储原始响应数据，用于删除操作
const originalResponseData = ref<{
  person: {
    person_id: string;
    canonical_name: string;
    aliases: string;
    relationships: string[];
    profile_summary: string;
    key_attributes: Record<string, string>;
    avatar: string;
    is_user: boolean;
  };
  events: { events: IEventItem[] };
} | null>(null);

// 获取懂量数据
const fetchIntimacyData = async (userId: string) => {
  try {
    console.log('🔄 [memoSidebar] 开始获取懂量数据, userId:', userId);

    const intimacyData = await getIntimacy({ user_id: userId });
    intimacyScore.value = intimacyData.intimacy_score || 0;

    console.log('✅ [memoSidebar] 懂量数据获取成功:', {
      intimacy_score: intimacyScore.value,
      level: intimacyData.level,
    });
  } catch (err) {
    console.error('❌ [memoSidebar] 获取懂量数据失败:', err);
    intimacyScore.value = '--';
  }
};

// 获取备忘录数据
const fetchMemoData = async () => {
  loading.value = true;
  error.value = '';

  try {
    // 获取用户信息以获取 mis_id
    let userId = '';

    if (userStore.userInfo?.login) {
      userId = userStore.userInfo.login;
    } else {
      // 如果 userStore 中没有用户信息，则直接调用接口获取
      const userInfo = await getUserInfo();
      userId = userInfo.login;
    }

    if (!userId) {
      error.value = '用户信息不存在';
      return;
    }

    console.log('🔄 [memoSidebar] 开始获取备忘录数据, userId:', userId);

    // 获取原始数据用于删除操作
    const rawData = await fetchInstance.fetch('/humanrelation/get_user_profile_and_events', {
      method: 'GET',
      params: {
        user_id: userId,
      },
    });
    originalResponseData.value = rawData;

    // 并行获取备忘录数据和懂量数据
    const [memoData] = await Promise.all([getMemoInfo(userId), fetchIntimacyData(userId)]);

    // 调用备忘录接口（数据处理已在API层完成）
    memoCategories.value = memoData;

    console.log('✅ [memoSidebar] 备忘录数据获取成功:', {
      categories_count: memoCategories.value.length,
      categories: memoCategories.value.map((cat) => ({ title: cat.title, items_count: cat.items.length })),
    });
  } catch (err) {
    console.error('❌ [memoSidebar] 获取备忘录数据失败:', err);
    error.value = '网络错误，请重试';
  } finally {
    loading.value = false;
  }
};

// 关闭侧边栏
const handleClose = () => {
  emit('close');
};

// 监听侧边栏打开状态
watch(
  () => props.isOpen,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden';
      // 侧边栏打开时获取数据
      void fetchMemoData();
    } else {
      document.body.style.overflow = '';
    }
  },
);

// 组件挂载时如果侧边栏已经打开，则获取数据
onMounted(() => {
  if (props.isOpen) {
    void fetchMemoData();
  }
});

// 处理删除备忘录项目
const handleDeleteMemoItem = (categoryTitle: string, item: IMemoItem) => {
  // 判断是事件还是属性
  const isEvent = categoryTitle === '重要事件';

  if (isEvent) {
    // 从原始数据中找到对应的事件ID
    const events = originalResponseData.value?.events?.events || [];
    const matchingEvent = events.find((event: IEventItem) => event.description_text === item.value);

    if (matchingEvent) {
      itemToDelete.value = {
        category: categoryTitle,
        item,
        type: 'event',
        eventId: matchingEvent.event_id,
      };
      deleteDialogContent.value = `确定要删除事件 "${item.value}" 吗？`;
      deleteDialogHint.value = '删除后将无法恢复该事件记录';
    } else {
      showFailToast('无法找到对应的事件记录');
      return;
    }
  } else {
    // 属性删除
    itemToDelete.value = {
      category: categoryTitle,
      item,
      type: 'attribute',
    };
    deleteDialogContent.value = `确定要删除 "${item.label}" 信息吗？`;
    deleteDialogHint.value = '删除后将无法恢复该信息';
  }

  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  itemToDelete.value = null;
  deleteDialogContent.value = '';
  deleteDialogHint.value = '';
};

// 确认删除备忘录项目
const confirmDeleteMemoItem = async () => {
  if (!itemToDelete.value) return;

  try {
    isDeleting.value = true;

    // 获取用户ID
    let userId = '';
    if (userStore.userInfo?.login) {
      userId = userStore.userInfo.login;
    } else {
      const userInfo = await getUserInfo();
      userId = userInfo.login;
    }

    if (itemToDelete.value.type === 'event' && itemToDelete.value.eventId) {
      // 删除事件
      const response = await deletePersonEvent({
        user_id: userId,
        event_id: itemToDelete.value.eventId,
      });

      if (response.result === 'success') {
        showSuccessToast('事件删除成功');
        // 重新获取数据
        await fetchMemoData();
      } else {
        showFailToast('删除事件失败');
      }
    } else if (itemToDelete.value.type === 'attribute') {
      // 删除属性 - 通过更新person信息来实现
      const personData = originalResponseData.value?.person;
      if (personData) {
        // 复制现有的key_attributes并移除要删除的属性
        const currentAttributes = { ...personData.key_attributes };
        delete currentAttributes[itemToDelete.value.item.label];

        const response = await updatePerson(personData.person_id, {
          user_id: userId,
          canonical_name: personData.canonical_name,
          aliases: personData.aliases || '',
          relationships: personData.relationships || [],
          profile_summary: personData.profile_summary || '',
          key_attributes: currentAttributes,
          avatar: personData.avatar || '',
          is_user: personData.is_user || false,
        });

        if (response.result === 'success') {
          showSuccessToast('信息删除成功');
          // 重新获取数据
          await fetchMemoData();
        } else {
          showFailToast('删除信息失败');
        }
      } else {
        showFailToast('无法获取用户信息');
      }
    }

    closeDeleteDialog();
  } catch (deleteError) {
    console.error('删除失败:', deleteError);
    showFailToast('删除失败，请重试');
  } finally {
    isDeleting.value = false;
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.memo-sidebar-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.memo-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 55%;
  height: 100%;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  box-shadow:
    var(--shadow-strong),
    0 0 0 1px var(--border-light);
  z-index: 1001;
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;

  &.sidebar-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
    background: transparent;
    border-bottom: 1px solid var(--border-light);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: var(--spacing-xl);
      right: var(--spacing-xl);
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
    }

    .title {
      font-size: var(--font-size-2xl);
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.5px;
    }

    .intimacy-display {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-right: var(--spacing-md);

      .intimacy-label {
        font-size: var(--font-size-xl);
        color: var(--text-secondary);
        font-weight: 500;
      }

      .intimacy-value {
        font-size: var(--font-size-xl);
        font-weight: 700;
        background: linear-gradient(135deg, var(--primary-color) 0%, #00ffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          filter: brightness(1.2);
        }
      }
    }

    .close-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: transparent;
      border: 1px solid transparent;
      color: var(--text-tertiary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: var(--bg-hover);
        border-color: var(--border-light);
        color: var(--text-primary);
        transform: scale(1.05);
        box-shadow: var(--shadow-medium);
      }

      &:active {
        transform: scale(0.95);
      }

      img {
        width: 20px;
        height: 20px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    background-color: transparent;
    /* 隐藏滚动条但保持可滚动 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .loading-text {
        font-size: 24px; // 增加4px
        color: var(--text-secondary);
        animation: pulse 1.5s ease-in-out infinite;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200px;
      gap: var(--spacing-md);

      .error-text {
        font-size: 24px; // 增加4px
        color: var(--text-tertiary);
        text-align: center;
      }

      .retry-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--primary-color);
        color: white;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-size: 22px; // 增加4px
        transition: all 0.3s ease;

        &:hover {
          background: var(--primary-color-hover);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .empty-text {
        font-size: 24px; // 增加4px
        color: var(--text-tertiary);
        text-align: center;
      }
    }

    .memo-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);

      .memo-category {
        .category-title {
          font-size: 26px; // 增加4px
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: var(--spacing-md);
          padding-bottom: var(--spacing-sm);
          border-bottom: 2px solid var(--primary-color);
          letter-spacing: -0.3px;
        }

        .memo-items {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);

          .memo-item {
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            background: var(--bg-glass);
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: space-between;

            &:hover {
              background: linear-gradient(135deg, rgba(0, 188, 212, 0.15) 0%, rgba(0, 255, 255, 0.1) 100%);
              border-color: rgba(0, 255, 255, 0.4);
              transform: translateY(-2px);
              box-shadow:
                0 6px 20px rgba(0, 255, 255, 0.2),
                0 0 15px rgba(0, 255, 255, 0.3);

              .memo-label {
                color: rgba(255, 255, 255, 0.9);
              }

              .memo-value {
                color: #ffffff;
              }

              .delete-btn {
                opacity: 1;
              }
            }

            .memo-content {
              flex: 1;
              min-width: 0;
              overflow: hidden;
            }

            .memo-label {
              font-size: 22px; // 增加4px
              font-weight: 600;
              color: var(--text-secondary);
              margin-bottom: 4px;
              transition: color 0.3s ease;
            }

            .memo-value {
              font-size: 22px; // 增加4px
              font-weight: 550;
              color: var(--text-primary);
              line-height: 1.4;
              transition: color 0.3s ease;
            }

            .memo-actions {
              display: flex;
              align-items: center;
              margin-left: 12px;
              flex-shrink: 0;

              .delete-btn {
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.2);
                cursor: pointer;
                transition: all 0.3s ease;
                opacity: 0.7;

                &:hover {
                  background: rgba(239, 68, 68, 0.2);
                  border-color: rgba(239, 68, 68, 0.4);
                  transform: scale(1.1);
                }

                img {
                  width: 18px;
                  height: 18px;
                  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg)
                    brightness(104%) contrast(97%);
                }
              }
            }
          }
        }
      }
    }
  }
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  transition: all 0.3s ease;
}

/* 添加一些微动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.memo-sidebar.sidebar-open {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay {
  animation: fadeIn 0.3s ease;
}
</style>
