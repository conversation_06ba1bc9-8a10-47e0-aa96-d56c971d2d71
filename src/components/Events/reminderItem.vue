<template>
  <div class="reminder-item" @click="handleClick">
    <!-- 右上角删除按钮 -->
    <button class="delete-btn" @click.stop="handleDeleteClick">
      <img src="@/assets/icon/close.png" alt="删除" class="delete-icon" />
    </button>
    <div class="reminder-text">
      {{ reminderData.reminder_text_template || '暂无提醒内容' }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { type IReminder } from '@/apis/memory';

interface IProps {
  reminderData: IReminder;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  click: [reminder: IReminder];
  deleteRequest: [reminder: IReminder];
}>();

const handleClick = () => {
  emit('click', props.reminderData);
};

// 处理删除按钮点击
const handleDeleteClick = () => {
  emit('deleteRequest', props.reminderData);
};
</script>

<style scoped lang="scss">
.reminder-item {
  width: 220px;
  height: 160px;
  background: rgba(1, 28, 32, 0.4);
  border: none;
  border-radius: 16px;
  padding: 15px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
      -6px 0 16px rgba(0, 255, 255, 0.4),
      0 12px 32px rgba(0, 0, 0, 0.3);
    border-left-color: #00ffff;
    background: rgba(1, 28, 32, 0.8);
  }

  &:active {
    transform: translateY(-4px) scale(1.02);
  }
}

// 删除按钮样式，与personDetailPopup中的close-btn保持一致
.delete-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  .delete-icon {
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
  }
}

.reminder-text {
  margin-top: 50px; // 为删除按钮留出空间
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28px; // 增加4px (原来14px)
  font-weight: 500;
  line-height: 1.5;
  overflow: hidden;
  word-break: break-word;
}
</style>
